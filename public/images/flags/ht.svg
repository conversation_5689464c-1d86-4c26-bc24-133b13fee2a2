<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 500 300">
<title>Flag of Haiti</title>
<rect width="500" height="300" fill="#d21034"/>
<rect width="500" height="150" fill="#00209f"/>
<g id="coa" fill="#f1b517" stroke="#000" stroke-width="0.1826">
<rect fill="#fff" stroke="none" width="94.6416" height="75.3413" x="202.679" y="112.284"/>
<path fill="#016a16" stroke="#016a16" d="m269.047,166.766-19.3141,0.56058-19.227,0.21862s-9.8781,3.65352-13.9162,4.4734c-4.02618,0.81746-3.50263,2.14025-6.61818,2.83651-2.49547,0.55768-2.78328,0.73353-4.24748,0.82965-0.88604,0.0582-1.97735,0.73987-3.04471,1.49113v10.4491h94.6416v-9.939c-1.02588-0.55902-2.0035-0.94359-3.00404-1.28372-1.39031-0.47263-2.32725-0.9895-4.82272-1.54718-3.11555-0.69626-2.592-2.01905-6.61818-2.83651-4.03807-0.81988-13.829-5.2526-13.829-5.2526z"/>
<g id="flags">
<g id="flags_half">
<g>
<path d="m244.529,143.512,1.39696-0.0329,2.55541,10.749-0.54516,3.38576-3.40721-14.1018z"/>
<path fill="#fff" d="m243.064,136.346,0.95402,8.2836,1.02216-0.0657-1.97618-8.21786z"/>
<path d="m245.722,154.589-4.29308-11.5379,1.60139-0.0986,5.0086,14.1018-2.31691-2.46536z"/>
<path fill="#fff" d="m239.419,136.477,1.77175,8.02063,1.43103,0-3.20278-8.02063z"/>
<path d="m241.906,150.546-2.52134-5.42379,1.09031-0.2301,4.59973,9.00677-3.1687-3.35288z"/>
<path fill="#fff" d="m236.08,138.778,2.79391,7.62618,1.43103,0-4.22494-7.62618z"/>
</g>
<g>
<path fill="#0a328c" d="m242.249,151.364-11.4356-12.4841c-3.57164,2.82814-2.90807,8.02116-2.90807,8.02116l12.957,10.1661,1.38665-5.70318z"/>
<path fill="#d20014" d="m248.861,158.235-8.4834-8.59356c-3.5866,0.12446-0.74067,5.47902-0.56005,6.15114l9.04345,7.73433v-5.29191z"/>
<path d="m248.139,156.757-17.5394-18.7806-0.6845,0.48355,18.5612,20.6216-0.3373-2.32459z"/>
<path d="m229.586,136.772-0.38503-0.44575,0.0385-0.89151-3.61925-2.30304,1.61711,3.6403,1.23209,0.22287,0.38503,0.44575,0.73155-0.66862z"/>
<path d="m227.738,138.11c0.61604-0.74292,1.34759-1.48584,2.65668-2.19161l1.00107,1.44869-2.15615,1.82015-1.5016-1.07723z"/>
<path fill="#0a328c" stroke="#0a328c" d="m225.811,141.278s0.5879,1.02632,0.5879,1.02632c0.36393-0.13504,4.03128-3.45709,4.03128-3.45709l2.63154-0.081,0.75587-0.83726c-1.93166-2.07966-3.75134,0.29709-3.75134,0.29709l-4.25525,3.05196z"/>
<path fill="#d20014" stroke="#d20014" d="m227.911,143.007c0.13998-0.91829,1.87567-3.21402,2.12762-3.29504,1.30317,0.39117,2.51537,1.14116,3.83533-1.83658-1.1478,1.45846-3.47139-0.027-3.47139-0.027l-1.00782,0.83726,0.028,0.40513-3.05146,2.8359,1.53973,1.08034z"/>
</g>
<g>
<path fill="#0a328c" d="m237.252,154.672-14.367-11.4113c-2.45972,2.50307-0.41467,10.2317-0.41467,10.2317l14.0422,7.6072,0.73945-6.42753z"/>
<path fill="#d20014" d="m249.166,163.917-11.8183-9.46696c-4.03608,0.56639-0.86322,6.50618-0.62257,7.16056l12.481,6.76847-0.0402-4.46207z"/>
<path d="m248.697,162.005-25.5539-19.1816-0.58707,0.59088,26.8371,20.9549-0.69613-2.36409z"/>
<path d="m221.928,141.807-0.45941-0.37447-0.12254-0.88441-3.97879-1.66173,2.24776,3.31408,1.25348,0.0131,0.45941,0.37447,0.60009-0.78102z"/>
<path d="m220.349,143.433c0.47297-0.83483,1.05969-1.68902,2.22186-2.60338l1.24661,1.25897-1.79578,2.15371-1.67269-0.8093z"/>
<path fill="#0a328c" stroke="#0a328c" d="m218.925,146.641s0.15919,1.06,0.15919,1.06c0.33756-0.1883,3.56107-3.72584,3.56107-3.72584l2.58599-0.47719,0.52682-0.83299c-1.79717-2.11364-3.71297,0.69754-3.71297,0.69754l-3.1201,3.27848z"/>
<path fill="#d20014" stroke="#d20014" d="m220.971,148.545c-0.12263-0.98212,1.21945-3.86265,1.45516-3.9807,1.54652,0.45977,2.8934,1.15261,3.46231-2.0687-0.89718,1.61373-3.46102,0.36219-3.46102,0.36219l-0.85966,0.97906,0.12134,0.53096-2.61004,3.36954,1.89191,0.80765z"/>
</g>
<g>
<path fill="#0a328c" d="m236.224,161.651c-0.19061-0.046-18.5172-10.4422-18.5172-10.4422-1.33424,2.25264-0.12758,5.52064,0.45732,6.02891,0.0339,1.4999-0.60253,2.30652,0.7066,5.43903,0.38609,2.03671,1.32844,3.91312,2.70015,5.02765,1.67114,6.41674,8.22234,6.84773,10.7458,1.62404l3.90743-7.67738z"/>
<path fill="#d20014" d="m248.352,168.512-12.5481-7.35467c-4.03608,0.56638,0.0855,8.05519,0.32619,8.70957l12.1161,4.30413,0.10579-5.65903z"/>
<path d="m246.243,165.846-28.3496-15.0889-0.48403,0.67221,29.9045,16.6466-1.07089-2.22992z"/>
<path d="m216.528,149.937-0.51447-0.30053-0.26444-0.85502-4.19926-1.04078,2.75747,2.93404,1.24017-0.17626,0.51447,0.30053,0.46606-0.86198z"/>
<path d="m215.232,151.782c0.33178-0.89594,0.77277-1.82816,1.77237-2.90666l1.4354,1.05533-1.42445,2.39822-1.78332-0.54689z"/>
<path fill="#0a328c" stroke="#0a328c" d="m214.1,155.181s0.59329,1.16676,0.59329,1.16676c0.3071-0.2318,3.00342-4.57817,3.00342-4.57817l2.492-0.81976,0.4691-1.01389c-2.00158-1.84905-3.49738,1.34225-3.49738,1.34225l-3.06043,3.90281z"/>
<path fill="#d20014" stroke="#d20014" d="m216.772,156.836c-0.20042-0.99867,0.48376-4.19683,0.70015-4.34538,1.56074,0.46564,2.86327,0.59786,3.22314-2.62209-0.90761,1.52943-3.32789,0.95334-3.32789,0.95334l-0.71005,1.08496,0.14955,0.37953-2.05871,3.95082,2.02381,0.59882z"/>
</g>
</g>
<use xlink:href="#flags_half" transform="matrix(-1,0,0,1,499.85852,0)"/>
</g>
<g id="tree">
<path fill="#016a16" stroke="#016a16" d="m248.757,125.603-0.46484,9.07011,2.89945,0-0.42417-8.99163,3.02233-0.42447-0.33476-3.58228-7.51056,0.28993,0.042,3.25311,2.77054,0.38523z"/>
<g id="tree_half" fill="#016a16">
<path d="m249.817,121.898c-7.76384-7.77113-13.8779-3.97919-15.3336-2.90247,0.96228,0.18793,1.82599-0.41145,2.27298-0.48045-0.28013,0.35359-0.86579,1.22948-0.86579,1.22948s2.40063-1.13341,2.66882-1.02991c-0.34602,0.27599-0.72786,0.93628-0.72786,0.93628,0.1788,0.069,1.52885-1.05714,1.79539-1.07672-0.41123,0.46573-0.53888,1.52761-0.53888,1.52761l1.16969-0.54452c0.30396,0.21562-0.0845,0.63021,0.1798,0.60852,1.94414-0.15959,4.67774,1.77726,5.05322,2.37237"/>
<path d="m247.895,123.321c-0.80914-1.85399-15.5878-5.00514-18.5596,0.24395,1.16313,0.29273,2.2757-0.97579,2.2757-0.97579l-0.10114,1.17094,1.97228-1.51246-0.708,1.56125,1.56771-1.26852-0.0506,1.26852,1.92171-2.09793-0.25285,1.21973,1.71942-0.73184-0.20229,1.02457"/>
<path d="m247.238,123.614c-3.79284-2.53704-9.6591-0.82942-9.6591-0.82942-3.74227,2.09793-5.73699,1.71946-6.22027,5.61075,0.354-0.14637,1.36543-1.3661,1.36543-1.3661l0.75857,1.51247,0.25285-2.14673,0.60686,1.65883,0.91028-2.73219,0.20228,1.85399,1.416-1.85399,0.65742,0.53668,0.60686-1.41488,0.55628,0.92699s0.96085-0.58547,1.51714-0.24394l1.06199-1.21973,0.25286,0.8782,0.91028-1.07336,0.354,0.78063,0.91028-0.82942-0.0506,0.58547,3.13541,0.0488"/>
<path d="m246.38,124.053c-2.64328-0.0494-10.2265,2.81155-12.1877,5.8059,0.78094-0.43617,1.26251-0.63483,1.26251-0.63483l0.38693,1.20328,0.47279-1.20328,0.10114,0.927c0.28991-0.72391,0.61743-0.80561,1.16314-0.0488,0,0,0.20228-1.12215,0.20228-1.12215l0.30343,1.07336,0.50571-1.17094s0.31812,1.26908,0.50571,1.21973c0,0,0.83854-2.19438,1.21371-2.29309l0.10114,1.36609,0.50571-1.36609,0.45515,1.02457,0.10114-1.46367,0.50571,1.12215s0.2652-1.10343,0.65743-1.31731l0.75856,0.68305,3.37743-2.42674"/>
<path d="m246.888,124.678c-3.52111,1.34951-6.67031,6.17871-6.42914,8.22623,1.56462-1.94163,1.22764-1.67427,1.92704,0.55939,0,0,0.26458-1.95434,0.52987-2.18701l0.45923,1.44665,0.17053-1.89204,0.46972,0.0217-0.12058-1.18663,0.69939,0.53515c-0.16335-0.6785-0.33764-0.90743,0.38588-1.48911,0,0-0.91645,0.11634-0.19294-1.53564l0.67528,0.65148s-0.12058-0.72128,0.12059-1.00049l2.48407-0.86089"/>
<path d="m247.229,125.04-1.33017,2.53368,0.68214,0.19743-1.53481,1.15167,1.29606-0.13162s-1.9782,0.72391-2.21694,2.6653l2.0123-0.88844s-1.70534,1.74397-1.94409,2.79692l1.15963-1.02005s-1.12552,1.21748-0.95499,3.06016l0.85267-0.95424c-0.20464,2.23753-0.13642,2.43496,0.88678,4.14602,0,0-0.27285-2.6324,0.0341-2.96145l2.25105,2.33625-1.26195-3.32339s1.39837,2.10591,2.25105,1.90848l-1.77356-2.6982,1.73945,1.41491-1.9782-2.96144,1.84177,0.55938s-1.33016-1.67815-1.22784-2.73111l1.26195,1.38201s-0.4775-1.57944-0.34107-2.04011l0.64803,0.13162-1.26195-1.44782,0.85267-0.16452-0.10232-0.95424,0.61392-0.19743-0.0341-1.05296"/>
<path d="m248.559,125.797c-2.18283,2.96144-1.05731,7.53523-2.35337,9.87148"/>
</g>
<use xlink:href="#tree_half" transform="matrix(-1,0,0,1,499.85867,0)"/>
<g id="trunk">
<path d="m250.945,166.535s1.18865-9.94478,1.18579-14.2254c-0.002-3.36865-0.81392-10.8349-0.81392-10.8349h-3.41702s-0.52001,7.42903-0.44204,10.7889c0.0994,4.28179,1.18578,14.2713,1.18578,14.2713h2.30141z"/>
<path d="m247.674,141.953,3.95963,0-0.46487-2.6659-2.87012,0-0.62464,2.6659z"/>
<path d="m247.927,139.816,3.53853,0-0.667-2.6659-2.17951,0-0.69202,2.6659z"/>
<path d="m248.155,137.655,2.94897,0-0.49856-2.6659-1.87631,0-0.5741,2.6659z"/>
<path d="m250.709,134.673-2.06274,0-0.20917,0.96419,2.44041,0-0.1685-0.96419z"/>
<path d="m251.132,164.367h-2.62035"/>
<path d="m251.275,162.42h-2.9404"/>
<path d="m251.664,160.444h-3.72683"/>
<path d="m251.856,158.291h-3.90057"/>
<path d="m251.883,156.139h-4.10175"/>
<path d="m252.043,154.171h-4.44923"/>
<path d="m252.053,152.38h-4.65041"/>
<path d="m252.025,150.219h-4.65042"/>
<path d="m251.87,148.084h-4.49496"/>
<path d="m251.664,145.914h-4.06517"/>
<path d="m251.467,143.955h-3.67197"/>
</g>
<g id="cap">
<path fill="#0a328c" stroke="#0a328c" d="m249.909,119.044c-0.68827-0.32391-2.24539-1.3126-1.78345-1.90279,0,0,0.71338-1.21454,0.71338-1.21454,1.01062-2.27664-0.0184-1.39459-0.12589-2.10521-0.10324-0.68238,3.86451-0.1495,3.92359,1.39673,0.0163,0.42592-0.2943,0.17596-0.25178,2.61126,0,0-0.75534,1.45746-0.75534,1.45746-0.56496,0.0526-1.23395-0.0139-1.72051-0.24291z"/>
<path d="m250.392,124.934-0.98614-0.82994,0.27276-7.10507,0.96516,0.28339-0.25178,7.65162z"/>
<path fill="#d20014" stroke="#d20014" d="m251.672,119.246c0.61915-0.36236,1.02443-1.44301,0.65043-2.08496-1.01607-1.28744-1.6737-0.7562-3.37806-1.336-0.3399,0.36203-1.05561,1.05866-0.90222,1.5789,2.24222,1.05562,1.37162,0.15654,2.1135,0.28804,0.39994,0.0709,0.65837,0.54508,1.51635,1.55402z"/>
</g>
</g>
<g id="items">
<g id="i1">
<use xlink:href="#ip" transform="matrix(1,0,0,-1,-0.00226132,320.36968)"/>
<path fill="#016a16" stroke="#fff" stroke-width="0.1811" stroke-linecap="round" d="m238.358,161.75c0,1.29873-0.81458,2.35278-1.81827,2.35278s-1.81827-1.05405-1.81827-2.35278v-1.27647h3.63654v1.27647z"/>
<ellipse fill="#fff" stroke="none" cx="236.539" cy="160.063" rx="1.806" ry="0.898"/>
<path fill="#016a16" stroke="#f1b517" d="m236.126,161.075-0.31022-3.29722,1.38188,0-0.22561,3.29722-0.84605,0z"/>
<path id="ip" fill="#016a16" stroke="#fff" stroke-width="0.2373" stroke-linecap="round" d="m238.538,160.118c-0.26498,0.49319-1.06061,0.85208-1.99881,0.85208-0.9382-0.00001-1.72802-0.35889-1.99301-0.85208-0.0611,0.11381-0.0988,0.23335-0.0988,0.35877,0,0.66892,0.93708,1.21084,2.09179,1.21084s2.09178-0.54192,2.09178-1.21084c0-0.12542-0.0318-0.24496-0.093-0.35877z"/>
</g>
<g id="items_half">
<path id="cannon_base" d="m246.035,172.786-7.06013-2.18936-3.0762-5.25446-10.8928-0.53517,0.3783,3.94069,9.27903,1.77619,0.88243,1.58127,10.6659,2.96795-0.17658-2.28711z"/>
<g id="i2">
<path fill="#503200" d="m238.662,174.254-11.1485-16.3982-0.54716,0.41156,11.0604,16.4667,0.63525-0.48015z"/>
<path fill="#fff" d="m228.551,158.5-1.19022-1.99181c-0.22281-0.36812-0.56118-0.49033-1.05837-0.22832-0.48411,0.25512-0.43705,0.75928-0.23987,1.12295l1.10042,1.85207,1.38804-0.75489z"/>
</g>
<g id="cannon">
<path d="m239.986,164.127-22.4964-2.28644-0.54691,3.27137,22.387,2.25127,0.6563-3.2362z"/>
<path d="m217.089,160.997-0.58337,4.5377,0.94798,0,0.6563-4.15077-1.02091-0.38693z"/>
<path d="m220.844,161.735-0.25522,4.08042,0.58337,0.0704,0.47399-3.62314-0.80214-0.52764z"/>
<path d="m236.085,163.494-0.54691,3.83419,1.09382-0.0352,0.43753-3.79901-0.98444,0z"/>
<path d="m239.436,163.599-0.77861,4.43415,1.23764,0.17939,0.35444-2.33199c0.0995,0.46856,0.53095,0.82404,1.04589,0.82404,0.58851,0,1.06914-0.46369,1.06914-1.03146s-0.48063-1.02585-1.06914-1.02585c-0.40919,0-0.78043,0.23995-0.9413,0.68697l0.23242-1.66237-1.15048-0.0729z"/>
<path d="m231.317,164.319-3.09381,3.48224,0.36095,0.42285,3.97038-2.71118-1.23752-1.19391z"/>
<path d="m224.124,173,3.09381-3.48225-0.36094-0.42284-3.97039,2.71118,1.23752,1.19391z"/>
<path d="m232.22,172.129-3.76413-2.91016-0.38673,0.2736,2.9649,3.75585,1.18596-1.11929z"/>
<path d="m222.938,165.413,3.8157,2.81067,0.46407-0.34823-2.99068-3.75585-1.28909,1.29341z"/>
<path d="m227.09,163.094,0.36461,4.57288,0.51357-0.0219,1.2071-4.45153-2.08528-0.0995z"/>
<path d="m228.341,174.189-0.3646-4.57289-0.56515-0.0528-0.69146,4.62565,1.62121,0z"/>
<path d="m233.401,168.026-4.79461,0.51005-0.0729,0.45729,4.84929,0.63317,0.0182-1.60051z"/>
<path d="m221.993,169.609,4.6808-0.73994,0.0987-0.36384-4.84175-0.54096,0.0622,1.64474z"/>
<ellipse cx="227.693" cy="168.548" rx="1.27" ry="1.225"/>
<path fill-rule="evenodd" stroke-linecap="round" d="m234.438,168.548c0,3.59202-3.02175,6.50727-6.74495,6.50727-3.72321,0-6.74496-2.91525-6.74496-6.50727,0-3.592,3.02175-6.50726,6.74496-6.50726,3.7232,0,6.74495,2.91526,6.74495,6.50726zm-1.34742,0.00001c0,2.87446-2.41811,5.20734-5.39755,5.20734s-5.39754-2.33288-5.39754-5.20734c0-2.87445,2.4181-5.20734,5.39754-5.20734s5.39755,2.33289,5.39755,5.20734z"/>
<path d="m223.48,165.264-1.05705-0.79595"/>
<path d="m231.034,164.493,0.90236-0.92031"/>
<path d="m232.967,172.701-1.10862-0.87056"/>
<path d="m223.48,173.696,0.95392-1.0198"/>
</g>
<g id="axe">
<path d="m246.196,162.122,1.15496,5.8864-0.94208,0.4962-1.01348-6.29841,0.8006-0.0842z"/>
<path fill="#fff" d="m245.592,161.364c-0.53548-0.22387-1.2316,0.15498-2.37396-1.29153-1.87831,1.72203-0.10709,1.98034,0.17849,3.68515,0.53115,0.21828,0.85836-1.32283,2.30256-1.48095,0.1785-0.0172,2.29785-0.20311,2.29785-0.20311s-0.53201-0.72513-0.53201-0.72513-1.47399,0.0387-1.87293,0.0156z"/>
<path fill="#fff" d="m246.359,162.518-0.21419-1.75647c-0.23204-0.61993-0.99956-0.0861-0.99956,0.27552l0.19634,1.61871,1.01741-0.13776z"/>
</g>
<path id="bugle" fill-rule="evenodd" d="m240.765,167.372,0.0188,1.63686c-0.53573,0.18339-0.454,0.58498-0.43672,1.16852l0.0188,4.77113c-0.1901,0.31678-0.77767,0.33153-1.05418,0.5797-0.2765,0.24817-0.24194,0.72975,1.01961,1.04653l0.97557,0.0349c1.48622-0.32512,1.79297-0.81731,1.0477-1.09485-0.39645-0.14764-0.91809-0.32374-1.11682-0.55716l0.033-1.1367,0.96456,0.0349c0.25923,0,0.7525-0.65168,0.76978-1.1852l0.005-2.47044c-0.0346-1.4005-1.08551-1.55803-1.3793-1.54136l0.0204-1.28978-0.88602,0.003zm1.63059,2.73012c0.0173-0.40014-0.5556-0.75694-0.78631-0.76694s-0.73447,0.46683-0.75175,0.86697v1.00036c0.0346,0.3668,0.32755,1.60924,0.91592,1.7006,0.27872,0.0433,0.63942-0.40014,0.63942-0.76694l-0.0173-2.03405z"/>
<g id="i3" fill="#fff">
<path d="m234.275,172.328c-0.63643-0.25722-0.63028,1.17186-0.63028,1.17186l1.00278,0.0105c0.43862,0.19913,0.79146,0.84264,0.51178,1.1585-0.23124,0.26115-2.17731-0.44106-2.30792-0.0372,0.34401,1.94159,2.91678,2.68867,3.84773,1.83931,0.91051-0.83072,1.14108-3.04781-1.81748-3.62862l-0.60661-0.51433z"/>
<path d="m234.87,172.856-0.29241,0.63889"/>
</g>
<g id="small_flag">
<path d="m214.943,173.055-0.96325-4.04912"/>
<path d="m216.112,172.822-1.47928-3.15301"/>
<path d="m218.658,171.993-3.33698-1.79223"/>
<path d="m220.103,171.628-6.19232-2.68836"/>
<path fill="#d20014" stroke="#d20014" d="m210.215,166.013s-2.25377-0.0356-2.65571,0.122c-0.16218,0.0636,0.56688,0.82456,0.41552,1.11549-0.56308,1.08226-4.3943,0.93396-4.69718,0.69717-0.51817-0.4051,3.56833-0.75265,3.59515-1.02833,0.0308-0.31645-0.60433-1.14672-0.50585-1.6558,0.12777-0.66051,1.78854-0.90633,1.78854-0.90633l2.05953,1.6558z"/>
<path fill="#503200" d="m213.233,168.05-5.10223-4.22211s-0.22478-0.2382-0.41282-0.0332c-0.18413,0.20074,0.0688,0.43146,0.0688,0.43146l5.26348,4.34783,0.18277-0.52399z"/>
<path d="m218.146,172.217c-0.0903-0.0871-4.1552-3.60789-4.1552-3.60789l-0.27099-1.02833s-0.6278,0.42527-0.85814,0.64488-0.58715,0.81919-0.58715,0.81919l1.13817,0.0523,3.59515,3.29416,1.13816-0.1743z"/>
</g>
<g id="cannonballs">
<ellipse cx="232.534" cy="177.056" rx="0.740" ry="0.715"/>
<ellipse cx="230.641" cy="175.716" rx="0.740" ry="0.715"/>
<ellipse cx="228.091" cy="176.593" rx="0.740" ry="0.715"/>
<ellipse cx="224.153" cy="177.445" rx="0.740" ry="0.715"/>
<ellipse cx="222.487" cy="177.64" rx="0.740" ry="0.715"/>
<ellipse cx="220.846" cy="177.64" rx="0.740" ry="0.715"/>
<ellipse cx="219.18" cy="177.591" rx="0.740" ry="0.715"/>
<ellipse cx="219.988" cy="176.325" rx="0.740" ry="0.715"/>
<ellipse cx="221.604" cy="176.398" rx="0.740" ry="0.715"/>
<ellipse cx="223.144" cy="176.203" rx="0.740" ry="0.715"/>
<ellipse cx="222.361" cy="174.961" rx="0.740" ry="0.715"/>
<ellipse cx="220.846" cy="174.937" rx="0.740" ry="0.715"/>
</g>
<path id="chain" fill-rule="evenodd" d="m236.47,177.991c0-0.31649-0.76859-0.18036-0.76859-0.55072,0-0.41154,1.17217-0.10903,1.33411,0.25303,0.65964-0.59607,0.94402-0.16037,1.34812,0.0197,0.57546-0.85539,1.28698-0.82343,1.98133-0.19706,0.40146-0.44287,1.40966-0.29647,1.75664,0.19706,0.66466-0.6591,1.25491-0.49671,1.71579-0.0197,0.18538-0.34652,0.45745-0.2707,0.65363-0.0197,0.35414-0.62828,1.24209-0.43967,1.61583-0.0249,0.33231-0.36526,0.67271-0.08,1.11376,0.18982,0.29784-0.2787,0.77827-0.22616,0.77827,0.0509,0,0.28889-0.62779,0.18719-0.62779,0.4739,0,0.25579,0.45796,0.12646,0.45796,0.45,0,0.25469-0.67973,0.35017-0.78477-0.0559-0.23021,0.43971-1.06186,0.27088-1.14386-0.13794-0.27669,0.33403-0.89704,0.43568-1.24599-0.0591-0.28657,0.23315-0.6295,0.26652-0.78695,0.0311-0.51709,0.55594-1.3887,0.4417-1.68895-0.17117,0,0-0.29346,0.0125-0.29346,0.0125-0.50794,0.68971-1.30179,0.37107-1.52011-0.0104-0.8527,0.61519-1.36211,0.42145-1.83835,0.0197-0.54713,0.66665-1.38081,0.62012-1.57281,0-0.36819,0.26829-1.13636,0.33144-1.13636,0.0176,0-0.30068,0.65255-0.19137,0.65255-0.46876zm1.65458,0.14977c0,0.1432-0.19492,0.25942-0.43509,0.25942s-0.43508-0.11622-0.43508-0.25942,0.19491-0.25943,0.43508-0.25943,0.43509,0.11623,0.43509,0.25943zm1.82033-0.21792c0,0.17238-0.23462,0.31227-0.52371,0.31227-0.28908,0-0.52371-0.13989-0.52371-0.31227,0-0.17236,0.23463-0.31226,0.52371-0.31226,0.28909,0,0.52371,0.1399,0.52371,0.31226zm3.57497,0.19716c0,0.1745-0.21232,0.31611-0.47392,0.31611s-0.47392-0.14161-0.47392-0.31611c0-0.17449,0.21232-0.31611,0.47392-0.31611s0.47392,0.14162,0.47392,0.31611zm-1.90344-0.083c0,0.22051-0.20766,0.39948-0.46353,0.39948s-0.46353-0.17897-0.46353-0.39948,0.20766-0.39948,0.46353-0.39948,0.46353,0.17897,0.46353,0.39948zm4.05961,0.0104c0,0.19712-0.18564,0.35711-0.41437,0.35711s-0.41436-0.15999-0.41436-0.35711c0-0.19713,0.18563-0.35711,0.41436-0.35711s0.41437,0.15998,0.41437,0.35711zm-1.24369,0.083c0,0.12739-0.11997,0.23078-0.26778,0.23078-0.14782,0-0.26778-0.10339-0.26778-0.23078s0.11996-0.23078,0.26778-0.23078c0.14781,0,0.26778,0.10339,0.26778,0.23078zm2.42185,0.0934c0,0.183-0.13521,0.33153-0.30181,0.33153s-0.30181-0.14853-0.30181-0.33153c0-0.18301,0.13521-0.33154,0.30181-0.33154s0.30181,0.14853,0.30181,0.33154z"/>
</g>
<use xlink:href="#items_half" transform="matrix(-1,0,0,1,499.85853,0)"/>
<g id="drum" stroke-width="0.231">
<path fill="#0a328c" stroke="#0a328c" d="m249.837,165.718c-2.5116,0-4.54963,0.94401-4.54963,2.10216,0,0.19861,0.0565,0.38965,0.1685,0.57178,0.54316,0.62909,2.30675,1.08752,4.39856,1.08752,1.95672,0,3.6243-0.40275,4.27654-0.9698,0.16439-0.21675,0.25566-0.44698,0.25566-0.6895,0-1.15816-2.03804-2.10216-4.54963-2.10216z"/>
<path stroke-width="0.1869" d="m254.427,167.981v7.05811c0,0.82757-2.04845,1.49922-4.57243,1.49922-2.52399,0-4.57244-0.67165-4.57244-1.49922v-7.05811c0,0.82757,2.04845,1.49922,4.57244,1.49922,2.52398,0,4.57243-0.67165,4.57243-1.49922z"/>
<path fill="#d20014" stroke="#d20014" stroke-width="0.1826" d="m245.282,167.982v1.08191l2.21961,6.84463,1.77221-5.2638,1.94652,5.46561,1.42429-5.15885,1.78311,3.46807v-1.91933l-1.65599-3.36345c-0.18576,0.0506-0.38384,0.094-0.59268,0.13454l-1.02846,3.81191-1.44681-3.6045c-0.24229-0.003-0.48334-0.008-0.7147-0.0224l-1.45263,3.62692-1.6618-4.36688c-0.37558-0.21764-0.59267-0.46661-0.59267-0.73436z"/>
<path fill="#0a328c" stroke="#0a328c" stroke-width="0.1869" d="m254.427,173.721v1.31782c0,0.82757-2.04845,1.49922-4.57243,1.49922-2.52399,0-4.57244-0.67165-4.57244-1.49922v-1.31782c0,0.82757,2.04845,1.49922,4.57244,1.49922,2.52398,0,4.57243-0.67165,4.57243-1.49922z"/>
<path stroke-width="0.1869" d="m251.737,175.085c-0.37176,0.0553-0.76859,0.0967-1.18535,0.11772l0.0116,1.31736c0.41688-0.0214,0.81373-0.062,1.18535-0.11773l-0.0116-1.31735z"/>
<path stroke-width="0.1869" d="m246.868,174.855-0.0116,1.31174c0.35286,0.10094,0.74975,0.18477,1.18534,0.24666v-1.31736c-0.43047-0.0611-0.82391-0.1417-1.17372-0.24104z"/>
<path stroke-width="0.1869" d="m245.282,173.723v1.31736c0,0.24576,0.18539,0.47354,0.50551,0.67829l0.0232-1.30053c-0.33594-0.20864-0.52875-0.44293-0.52875-0.69512z"/>
<path stroke-width="0.1869" d="m254.428,173.863-0.0232,0.006c-0.0801,0.26749-0.37874,0.51556-0.8309,0.72315l0.006,1.31174c0.53035-0.2448,0.84834-0.54036,0.84834-0.86328v-1.17721z"/>
<path fill="none" stroke-width="0.1869" d="m254.427,168.027v7.01203c0,0.82757-2.04845,1.49922-4.57243,1.49922-2.52399,0-4.57244-0.67165-4.57244-1.49922v-7.01203"/>
<path fill="#fff" stroke="#fff" stroke-width="0.1916" d="m253.392,167.52c0,0.50111-1.58376,0.90734-3.53741,0.90734-1.95366,0-3.53741-0.40623-3.53741-0.90734s1.58375-0.90734,3.53741-0.90734c1.95365,0,3.53741,0.40623,3.53741,0.90734z"/>
<path d="m248.698,168.19,0.15688,1.25008c0.30701,0.0223,0.62606,0.0382,0.95293,0.0392l0.23242-1.28932h-1.34223z"/>
<path d="m252.801,167.887-0.91225,0.24665,0.0581,1.17721c0.3102-0.0525,0.60208-0.11708,0.86576-0.1906l-0.0116-1.23326z"/>
<path d="m247.397,165.979c-0.25878,0.0764-0.50082,0.16525-0.7205,0.26347l0.12783,0.94417,0.58105-0.18499,0.0116-1.02265z"/>
<path d="m249.837,165.606c-0.12168,0-0.24098,0.007-0.36025,0.0112l0.19756,1.18249,0.60429-0.0112,0.23823-1.15447c-0.22219-0.0155-0.44837-0.028-0.67983-0.028z"/>
<path d="m252.51,166.024-0.12203,1.0337,0.69146,0.24441,0.0755-1.01464c-0.19636-0.0968-0.41236-0.18511-0.64496-0.26347z"/>
<path stroke-width="0.1826" d="m245.993,168.8,0.18421-1.28763-0.93496-0.50634,0.0278,1.46536,0.72294,0.32861z"/>
<path stroke-width="0.1826" d="m254.396,168.522,0.0417-1.28759-0.87584,0.26154,0.83413,1.02605z"/>
</g>
<g id="helmet">
<path d="m263.686,160.029c0.83083-0.11451,2.06245,1.02446,2.13643,2.4333,0.0587,1.11791-1.69134,1.0592-1.69134,1.0592-0.089,0.45803-1.0541,0.88186-1.83971,0.91606-0.76216,0.0332,0.20771-1.43135,0.20771-1.43135-0.47476-1.25959,0.14836-2.74819,0.14836-2.74819l1.03855-0.22902z"/>
<path d="m262.499,162.948,0.80116-0.0286s0.0613,0.41314,0.20771,0.42941c0.15745,0.0175,0.3264-0.40078,0.3264-0.40078l1.21658-0.0286"/>
<path fill="#d20014" stroke="#f1b517" d="m264.131,160.859c0.67974-0.49144,0.084-1.48026-0.38575-1.4886,0,0-1.09789,0.0286-1.09789,0.0286-0.51741,0.0107-1.39215,0.57267-1.27592,1.05921,0.10134,0.42425,0.38608,0.61772,0.83083,0.54391,0.20197-0.0335,0.62612-0.56336,0.83084-0.57254,0.27465-0.0123,0.87787,0.58848,1.09789,0.4294z"/>
</g>
<path id="anchor" fill-rule="evenodd" d="m214.242,174.855c-0.61672,0.078-0.73098,0.78846-0.4997,1.44067-2.68636,0.7552-5.08627,1.73945-7.45489,2.40487-0.0953-0.4735,1.17372-1.84429,1.17372-1.84429,0.33571,0.16194,0.58686,0.93056,0.58686,0.93056,0.0839-1.57891,1.63857-2.6291,1.63857-2.6291-0.9232-0.12146-3.73165,0.96732-4.06736,1.41265,0,0,1.08722-0.12146,1.25507,0,0,0-2.14094,2.31168-2.23705,2.99907,0.33303,0.75114,4.02669,1.31175,4.02669,1.31175-0.0583,0.19445-1.08657,0.93056-1.08657,0.93056,1.4691,0.32268,3.68473-0.71797,4.34627-1.35099,0,0-1.72808-0.14248-3.07377-1.23327,0,0,0.2775,0.67919,0.1104,1.00343,0,0-2.09936-0.60528-2.31839-0.95297,0.006-0.0301,4.96602-1.31472,7.36773-2.00126,0.0889,0.42909,0.56583,1.23978,1.13886,1.04828,0.37379-0.12492,0.25537-0.8657,0.11621-1.39023,0.13517,0.11606,0.31047,0.18499,0.50551,0.18499,0.42228,0,0.76699-0.32696,0.76699-0.73435,0-0.4074-0.34471-0.73996-0.76699-0.73996-0.33769,0-0.61915,0.21145-0.7205,0.50452-0.0956-0.45275-0.34512-1.35347-0.80766-1.29493zm1.8887,1.52779c0,0.19057-0.16032,0.34523-0.35785,0.34523s-0.35784-0.15466-0.35784-0.34523,0.16031-0.34524,0.35784-0.34524,0.35785,0.15467,0.35785,0.34524z"/>
<g id="i4">
<path d="m293.068,177.884c0.18296-0.36659-0.16181-2.21178-0.16181-2.21178-0.37288,0.0414-0.88217,0.68114-0.88217,0.68114,0.48088-1.51138-0.86509-4.03755-0.86509-4.03755,0.91058,0.1905,3.94542,3.12828,4.103,3.65753,0,0-1.32902-0.47414-1.52981-0.4154,0.21676,1.50571,1.25802,2.20544,1.64916,2.98821,0,0-1.7257,0.37897-1.55229,1.82194l-1.12718,0.0209c-0.39017-0.48099,0.15174-1.27567,0.15174-1.27567l-7.73851-3.24146c-0.49856-0.20913-1.6204-0.71815-1.45233-1.3175,0.16946-0.6043,2.01592,0.25096,2.47112,0.39734l6.93417,2.93229z"/>
<path d="m285.635,176.541,0.91042-1.67302c0.32514-0.43916-0.34683-0.92015-0.82371-0.35551l-1.0188,1.52662c-0.4552,0.83651,0.49856,1.06655,0.93209,0.50191z"/>
</g>
</g>
<g id="motto">
<g fill="#fff">
<path d="m231.367,180.366-0.0631-1.82484-2.20675,0.12166-0.1261,1.98705,2.3959-0.28387z"/>
<path d="m215.237,182.924-2.9508-1.65512c12.1686,1.11518,14.6537-3.12916,17.5942-3.35618,1.80755-0.14118,2.0898,2.0019-1.41275,0.98242l0.90579,2.24135s-8.6385,4.85952-17.1292,2.85219l2.99283-1.06466z"/>
<path d="m268.249,180.366,0.063-1.82484,2.6992,0.0623,0.1261,1.98705-2.88835-0.22448z"/>
<path d="m284.939,182.477,2.77669-1.51514c-13.9388,0.41529-10.7652-2.28929-17.7392-3.04823-1.80213-0.1953-3.15679,2.12067,1.41275,0.98242l-0.90579,2.24135s2.1922,0.002,2.1922,0.002c4.74805,3.12663,11.156,3.40575,15.4013,2.40209l-3.13793-1.06466z"/>
<path d="m228.418,178.908,0,3.15421s7.728,2.86747,21.5188,2.86747c13.8501,0,21.5188-2.86747,21.5188-2.86747v-3.15421s-5.84903,2.69542-21.5188,2.69542c-15.6103,0-21.5188-2.69542-21.5188-2.69542z"/>
</g>
<path fill="#000" stroke="none" d="m229.573,182.08,0.68194-2.30932,0.31676,0.0871-0.60146,2.0368,1.17887,0.32402-0.0805,0.27252-1.49564-0.41108zm2.23639-1.01549,0.0381-0.45359,0.10174-0.38911,0.33747,0.0821-0.10174,0.38911-0.1928,0.41593-0.18273-0.0445zm3.32525-0.15591,0.32275,0.0634-0.28686,1.35944c-0.0499,0.23647-0.11725,0.41881-0.20205,0.54702s-0.20878,0.22171-0.37192,0.28049c-0.16314,0.0588-0.36227,0.0651-0.5974,0.0189-0.22847-0.0449-0.40734-0.11956-0.5366-0.22407-0.12926-0.1045-0.20976-0.23481-0.2415-0.39093-0.0317-0.15611-0.0213-0.35882,0.0313-0.60813l0.28685-1.35945,0.32275,0.0634-0.28652,1.35784c-0.0431,0.20437-0.0552,0.35884-0.0363,0.46341s0.0708,0.19249,0.15571,0.26376c0.0849,0.0713,0.19663,0.12052,0.33527,0.14775,0.23734,0.0466,0.41743,0.0279,0.54026-0.056,0.12283-0.084,0.2154-0.27362,0.27771-0.56894l0.28652-1.35784zm0.36326,2.51013,0.40178-2.36971,0.33342,0.0526,0.97468,2.06413,0.31545-1.86053,0.31164,0.0492-0.40178,2.36971-0.33342-0.0526-0.9744-2.06574-0.31573,1.86215-0.31164-0.0492zm2.54115,0.38367,0.34172-2.37847,0.32625,0.0436-0.34173,2.37846-0.32624-0.0436zm0.9374-1.04907c0.0469-0.39598,0.19381-0.69384,0.44075-0.89358,0.24694-0.19973,0.54471-0.28038,0.89332-0.24196,0.22827,0.0252,0.42783,0.10047,0.59866,0.22591,0.17084,0.12544,0.29318,0.28758,0.36704,0.4864,0.0739,0.19883,0.0966,0.41759,0.0684,0.65626-0.0286,0.24193-0.10489,0.45279-0.22871,0.63258-0.12382,0.17978-0.28432,0.30854-0.48149,0.38627-0.19718,0.0777-0.40316,0.10475-0.61794,0.0811-0.23278-0.0257-0.43439-0.10284-0.60484-0.23154-0.17044-0.12869-0.29178-0.29209-0.36402-0.49019-0.0722-0.1981-0.0959-0.40184-0.0711-0.61123zm0.33678,0.0421c-0.0341,0.28749,0.0192,0.5228,0.15989,0.70591,0.14065,0.18311,0.33186,0.28799,0.57364,0.31464,0.24627,0.0272,0.45885-0.034,0.63776-0.18358,0.17891-0.14953,0.28654-0.37781,0.32291-0.68483,0.023-0.1942,0.009-0.36746-0.0418-0.5198-0.0509-0.15233-0.13711-0.27559-0.25867-0.36977-0.12156-0.0942-0.26387-0.15026-0.42692-0.16823-0.23166-0.0255-0.44007,0.0293-0.62524,0.16435-0.18517,0.1351-0.29903,0.38221-0.34155,0.74131zm2.30844,1.41617,0.2028-2.39322,0.33674,0.0266,1.1437,1.98175,0.15922-1.87899,0.31474,0.0248-0.20281,2.39322-0.33673-0.0266-1.14356-1.98339-0.15937,1.88062-0.31473-0.0248zm3.48923,0.24265,0.10868-2.39892,1.67748,0.0707-0.0128,0.28309-1.34843-0.0569-0.0336,0.74292,1.16694,0.0492-0.0128,0.28309-1.16694-0.0492-0.0494,1.08982-0.32905-0.0139zm1.83353,0.0783,1.00383-2.38291,0.35477,0.007,0.97029,2.41977-0.37513-0.007-0.27567-0.73253-1.04054-0.0194-0.28787,0.722-0.34968-0.007zm0.7378-0.97244,0.84363,0.0157-0.24637-0.66973c-0.0752-0.20345-0.13067-0.3705-0.16654-0.50113-0.0348,0.15334-0.0825,0.30535-0.1433,0.45603l-0.28742,0.69907zm1.9137,1.01069,0.005-2.4012,0.32936,0.00054-0.005,2.4012-0.32937-0.00054zm1.54203-0.007-0.0313-2.11763-0.81994,0.0113-0.004-0.28333,1.97259-0.0271,0.004,0.28333-0.82333,0.0113,0.0313,2.11763-0.32933,0.005zm2.44223-0.0577-0.1164-2.39858,0.32901-0.0148,0.10266,2.11553,1.22444-0.0553,0.0137,0.28305-1.55345,0.0702zm1.67472-0.073,0.77655-2.46067,0.35394-0.0243,1.19294,2.3253-0.37425,0.0257-0.34315-0.7055-1.0381,0.0714-0.21908,0.74414-0.34885,0.024zm0.6437-1.03278,0.84166-0.0579-0.30809-0.64551c-0.0939-0.19606-0.16484-0.35758-0.21279-0.48456-0.0203,0.15575-0.0536,0.3113-0.10001,0.46665l-0.22077,0.72127zm2.92174,0.75811-0.27296-2.38673,1.66896-0.17765,0.0322,0.28165-1.34158,0.14281,0.0845,0.73913,1.16102-0.12358,0.0322,0.28165-1.16101,0.12359,0.124,1.08428-0.32738,0.0349zm1.82509-1.37024c-0.0584-0.39457,0.006-0.71858,0.19234-0.97205,0.18659-0.25346,0.45356-0.40411,0.80091-0.45194,0.22746-0.0313,0.44026-0.007,0.63841,0.0726,0.19815,0.0797,0.359,0.20675,0.48255,0.38111,0.12354,0.17436,0.20291,0.38045,0.23809,0.61827,0.0357,0.24107,0.0171,0.46367-0.0556,0.66781s-0.19425,0.36785-0.36465,0.49114c-0.17041,0.12329-0.36262,0.19966-0.57664,0.22913-0.23194,0.0319-0.44722,0.006-0.64585-0.0765-0.19863-0.083-0.35884-0.21146-0.48063-0.38551-0.12178-0.17406-0.19811-0.3654-0.22897-0.57403zm0.33686-0.0414c0.0424,0.28646,0.1556,0.50113,0.33965,0.644,0.18406,0.14286,0.39654,0.19771,0.63745,0.16454,0.24538-0.0338,0.43503-0.14483,0.56896-0.33314,0.13392-0.1883,0.17825-0.43541,0.133-0.74134-0.0286-0.19349-0.0875-0.35773-0.17665-0.49271-0.0891-0.13498-0.20485-0.23321-0.34714-0.29469-0.14229-0.0615-0.29467-0.081-0.45714-0.0587-0.23082,0.0318-0.41811,0.13561-0.56188,0.31148-0.14376,0.17588-0.18918,0.44272-0.13625,0.80053zm2.61378,0.80601-0.4445-2.3626,1.0858-0.19014c0.21827-0.0382,0.38819-0.0461,0.50977-0.0235,0.12158,0.0225,0.22644,0.0816,0.31458,0.17713,0.0881,0.0956,0.14445,0.20832,0.16891,0.33832,0.0315,0.16761,0.002,0.31874-0.089,0.4534-0.0909,0.13466-0.25083,0.23814-0.47995,0.31044,0.0932,0.0247,0.16576,0.0525,0.21758,0.0834,0.11042,0.0672,0.21978,0.15629,0.32807,0.26717l0.54695,0.56843-0.40759,0.0714-0.41655-0.43479c-0.12135-0.12524-0.21972-0.2201-0.29512-0.28459-0.0754-0.0645-0.14015-0.10752-0.19425-0.12912s-0.10733-0.0339-0.15966-0.037c-0.0382-0.001-0.099,0.006-0.18254,0.0203l-0.37585,0.0658,0.19739,1.04915-0.32407,0.0568zm0.0757-1.37664,0.69658-0.12199c0.14812-0.0259,0.26116-0.061,0.33912-0.10516,0.078-0.0442,0.13255-0.10256,0.16375-0.17517,0.0312-0.0726,0.0396-0.14705,0.0253-0.22333-0.021-0.11174-0.0804-0.19624-0.17797-0.2535-0.0976-0.0573-0.2372-0.07-0.41872-0.0382l-0.7751,0.13573,0.14706,0.78163zm3.93115-0.26168,0.33959,0.009c-0.008,0.26923-0.0838,0.4897-0.22765,0.66141-0.14382,0.17171-0.34216,0.28502-0.59502,0.33993-0.2617,0.0568-0.48653,0.0517-0.67449-0.0155s-0.34748-0.18896-0.47854-0.36533c-0.13107-0.17636-0.2227-0.3764-0.2749-0.60011-0.0569-0.24395-0.0583-0.46723-0.004-0.66985,0.0542-0.20262,0.15952-0.37043,0.31585-0.50344s0.3416-0.22278,0.55582-0.2693c0.24293-0.0528,0.46112-0.0375,0.6546,0.0459,0.19347,0.0833,0.34801,0.22607,0.46362,0.42817l-0.29958,0.14061c-0.0961-0.15821-0.20676-0.26457-0.33201-0.31908-0.12525-0.0545-0.26904-0.0641-0.43136-0.0289-0.18661,0.0405-0.33251,0.11755-0.4377,0.23105-0.1052,0.11351-0.16622,0.24849-0.18309,0.40493-0.0169,0.15644-0.007,0.3119,0.0288,0.46637,0.0465,0.19921,0.11715,0.36658,0.212,0.50212,0.0949,0.13554,0.21436,0.22627,0.35849,0.2722,0.14414,0.0459,0.29018,0.0528,0.43815,0.0207,0.17998-0.0391,0.32068-0.12225,0.42209-0.24949,0.10141-0.12723,0.1512-0.29419,0.14939-0.50086zm0.91487,0.65288-0.66928-2.31277,1.73334-0.46687,0.079,0.27293-1.4161,0.38142,0.20498,0.70835,1.32617-0.3572,0.0785,0.27134-1.32617,0.3572,0.22781,0.78723,1.47171-0.3964,0.079,0.27292-1.78894,0.48185z"/>
</g>
</g>
</svg>
